<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GraphRAG 论文分析系统</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://unpkg.com/element-plus/dist/index.full.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <link rel="stylesheet" href="https://unpkg.com/element-plus/dist/index.css">
    <style>
        body {
            margin: 0;
            font-family: 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', '微软雅黑', Arial, sans-serif;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #d9d9d9;
            border-radius: 6px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #409eff;
        }
        .paper-card {
            margin: 10px 0;
            border: 1px solid #ebeef5;
            border-radius: 4px;
            padding: 15px;
        }
        .summary-content {
            background: #f5f7fa;
            padding: 20px;
            border-radius: 4px;
            margin: 10px 0;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .progress-container {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div id="app">
        <div class="header">
            <h1>GraphRAG 论文分析系统</h1>
            <p>基于 Deepseek + GraphRAG 的智能论文总结与综述生成</p>
        </div>

        <div class="container">
            <el-tabs v-model="activeTab" type="card">
                <!-- 论文上传 -->
                <el-tab-pane label="论文上传" name="upload">
                    <div class="upload-area">
                        <el-upload
                            ref="upload"
                            :action="apiBase + '/api/papers/upload'"
                            :on-success="handleUploadSuccess"
                            :on-error="handleUploadError"
                            :before-upload="beforeUpload"
                            :show-file-list="false"
                            drag
                            accept=".pdf,.docx,.txt">
                            <el-icon class="el-icon--upload"><upload-filled /></el-icon>
                            <div class="el-upload__text">
                                将文件拖到此处，或<em>点击上传</em>
                            </div>
                            <div class="el-upload__tip">
                                支持 PDF、DOCX、TXT 格式，文件大小不超过 50MB
                            </div>
                        </el-upload>
                    </div>

                    <el-divider content-position="left">已上传论文</el-divider>

                    <div v-if="papers.length === 0" style="text-align: center; color: #909399; padding: 40px;">
                        暂无上传的论文
                    </div>

                    <div v-for="paper in papers" :key="paper.id" class="paper-card">
                        <el-row :gutter="20">
                            <el-col :span="18">
                                <h4>{{ paper.title }}</h4>
                                <p><strong>作者:</strong> {{ paper.authors.join(', ') || '未知' }}</p>
                                <p><strong>摘要:</strong> {{ paper.abstract || '无摘要' }}</p>
                                <p><strong>文件:</strong> {{ paper.filename }} ({{ formatFileSize(paper.file_size) }})</p>
                            </el-col>
                            <el-col :span="6" style="text-align: right;">
                                <el-tag :type="getStatusType(paper.status)">{{ getStatusText(paper.status) }}</el-tag>
                                <br><br>
                                <el-checkbox v-model="selectedPapers" :label="paper.id">选择</el-checkbox>
                                <br><br>
                                <el-button type="danger" size="small" @click="deletePaper(paper.id)">删除</el-button>
                            </el-col>
                        </el-row>
                    </div>
                </el-tab-pane>

                <!-- 生成总结 -->
                <el-tab-pane label="生成总结" name="summary">
                    <el-form :model="summaryForm" label-width="120px">
                        <el-form-item label="选择论文">
                            <el-select v-model="summaryForm.paper_ids" multiple placeholder="请选择要分析的论文" style="width: 100%;">
                                <el-option
                                    v-for="paper in papers"
                                    :key="paper.id"
                                    :label="paper.title"
                                    :value="paper.id">
                                </el-option>
                            </el-select>
                        </el-form-item>

                        <el-form-item label="分析类型">
                            <el-radio-group v-model="summaryForm.summary_type">
                                <el-radio label="single_paper">单篇论文总结</el-radio>
                                <el-radio label="multi_paper_review">多篇论文综述</el-radio>
                            </el-radio-group>
                        </el-form-item>

                        <el-form-item label="输出语言">
                            <el-radio-group v-model="summaryForm.language">
                                <el-radio label="zh">中文</el-radio>
                                <el-radio label="en">英文</el-radio>
                            </el-radio-group>
                        </el-form-item>

                        <el-form-item label="自定义要求">
                            <el-input
                                v-model="summaryForm.custom_prompt"
                                type="textarea"
                                :rows="3"
                                placeholder="请输入特殊要求或关注点（可选）">
                            </el-input>
                        </el-form-item>

                        <el-form-item>
                            <el-button type="primary" @click="generateSummary" :loading="generating">
                                {{ generating ? '生成中...' : '开始生成' }}
                            </el-button>
                        </el-form-item>
                    </el-form>

                    <!-- 生成进度 -->
                    <div v-if="currentTask" class="progress-container">
                        <el-card>
                            <h4>生成进度</h4>
                            <el-progress :percentage="currentTask.progress" :status="getProgressStatus(currentTask.status)"></el-progress>
                            <p>{{ currentTask.message }}</p>
                        </el-card>
                    </div>
                </el-tab-pane>

                <!-- 查看结果 -->
                <el-tab-pane label="查看结果" name="results">
                    <div v-if="summaries.length === 0" style="text-align: center; color: #909399; padding: 40px;">
                        暂无生成的总结
                    </div>

                    <div v-for="summary in summaries" :key="summary.id">
                        <el-card style="margin: 20px 0;">
                            <template #header>
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <span>{{ summary.title }}</span>
                                    <el-tag :type="summary.summary_type === 'single_paper' ? 'primary' : 'success'">
                                        {{ summary.summary_type === 'single_paper' ? '单篇总结' : '文献综述' }}
                                    </el-tag>
                                </div>
                            </template>
                            <div class="summary-content">{{ summary.content }}</div>
                            <div style="margin-top: 10px; color: #909399; font-size: 12px;">
                                生成时间: {{ formatDate(summary.created_time) }} |
                                论文数量: {{ summary.paper_count }} 篇 |
                                语言: {{ summary.language === 'zh' ? '中文' : '英文' }}
                            </div>
                        </el-card>
                    </div>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>

    <script>
        const { createApp } = Vue;
        const { ElMessage, ElMessageBox } = ElementPlus;

        createApp({
            data() {
                return {
                    apiBase: 'http://localhost:9000',
                    activeTab: 'upload',
                    papers: [],
                    summaries: [],
                    selectedPapers: [],
                    generating: false,
                    currentTask: null,
                    summaryForm: {
                        paper_ids: [],
                        summary_type: 'single_paper',
                        language: 'zh',
                        custom_prompt: ''
                    }
                }
            },
            mounted() {
                this.loadPapers();
                this.loadSummaries();
            },
            methods: {
                async loadPapers() {
                    try {
                        const response = await axios.get(`${this.apiBase}/api/papers/`);
                        this.papers = response.data.papers;
                    } catch (error) {
                        ElMessage.error('加载论文列表失败');
                    }
                },
                async loadSummaries() {
                    try {
                        const response = await axios.get(`${this.apiBase}/api/summary/`);
                        this.summaries = response.data.summaries;
                    } catch (error) {
                        ElMessage.error('加载总结列表失败');
                    }
                },
                beforeUpload(file) {
                    const isValidType = ['application/pdf', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'].includes(file.type);
                    const isLt50M = file.size / 1024 / 1024 < 50;

                    if (!isValidType) {
                        ElMessage.error('只支持 PDF、DOCX、TXT 格式的文件!');
                    }
                    if (!isLt50M) {
                        ElMessage.error('文件大小不能超过 50MB!');
                    }
                    return isValidType && isLt50M;
                },
                handleUploadSuccess(response) {
                    ElMessage.success('文件上传成功!');
                    this.loadPapers();
                },
                handleUploadError(error) {
                    ElMessage.error('文件上传失败!');
                },
                async deletePaper(paperId) {
                    try {
                        await ElMessageBox.confirm('确定要删除这篇论文吗？', '确认删除', {
                            confirmButtonText: '确定',
                            cancelButtonText: '取消',
                            type: 'warning'
                        });

                        await axios.delete(`${this.apiBase}/api/papers/${paperId}`);
                        ElMessage.success('删除成功');
                        this.loadPapers();
                    } catch (error) {
                        if (error !== 'cancel') {
                            ElMessage.error('删除失败');
                        }
                    }
                },
                async generateSummary() {
                    if (this.summaryForm.paper_ids.length === 0) {
                        ElMessage.warning('请选择要分析的论文');
                        return;
                    }

                    if (this.summaryForm.summary_type === 'single_paper' && this.summaryForm.paper_ids.length !== 1) {
                        ElMessage.warning('单篇论文总结只能选择一篇论文');
                        return;
                    }

                    if (this.summaryForm.summary_type === 'multi_paper_review' && this.summaryForm.paper_ids.length < 2) {
                        ElMessage.warning('文献综述至少需要选择两篇论文');
                        return;
                    }

                    try {
                        this.generating = true;
                        const response = await axios.post(`${this.apiBase}/api/summary/generate`, this.summaryForm);
                        const taskId = response.data.task_id;

                        ElMessage.success('开始生成，请稍候...');
                        this.pollProgress(taskId);

                    } catch (error) {
                        this.generating = false;
                        ElMessage.error('生成失败: ' + (error.response?.data?.detail || error.message));
                    }
                },
                async pollProgress(taskId) {
                    try {
                        const response = await axios.get(`${this.apiBase}/api/summary/progress/${taskId}`);
                        this.currentTask = response.data;

                        if (this.currentTask.status === 'completed') {
                            this.generating = false;
                            this.currentTask = null;
                            ElMessage.success('生成完成!');
                            this.loadSummaries();
                            this.activeTab = 'results';
                        } else if (this.currentTask.status === 'error') {
                            this.generating = false;
                            this.currentTask = null;
                            ElMessage.error('生成失败: ' + this.currentTask.message);
                        } else {
                            // 继续轮询
                            setTimeout(() => this.pollProgress(taskId), 2000);
                        }
                    } catch (error) {
                        this.generating = false;
                        this.currentTask = null;
                        ElMessage.error('获取进度失败');
                    }
                },
                getStatusType(status) {
                    const types = {
                        'uploaded': 'info',
                        'processing': 'warning',
                        'processed': 'success',
                        'error': 'danger'
                    };
                    return types[status] || 'info';
                },
                getStatusText(status) {
                    const texts = {
                        'uploaded': '已上传',
                        'processing': '处理中',
                        'processed': '已处理',
                        'error': '错误'
                    };
                    return texts[status] || status;
                },
                getProgressStatus(status) {
                    if (status === 'completed') return 'success';
                    if (status === 'error') return 'exception';
                    return '';
                },
                formatFileSize(bytes) {
                    if (bytes === 0) return '0 B';
                    const k = 1024;
                    const sizes = ['B', 'KB', 'MB', 'GB'];
                    const i = Math.floor(Math.log(bytes) / Math.log(k));
                    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
                },
                formatDate(dateString) {
                    return new Date(dateString).toLocaleString('zh-CN');
                }
            }
        }).use(ElementPlus).mount('#app');
    </script>
</body>
</html>
