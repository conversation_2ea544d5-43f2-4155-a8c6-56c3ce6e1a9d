# GraphRAG 论文分析系统

基于 Deepseek + GraphRAG 的智能论文总结与综述生成系统

## 系统概述

本系统是一个智能论文分析平台，结合了 GraphRAG（图检索增强生成）技术和 Deepseek 大语言模型，能够实现：

- **单篇论文总结**：对单篇学术论文进行深度分析和总结
- **多篇论文综述**：基于多篇相关论文生成文献综述
- **智能知识图谱**：利用 GraphRAG 构建论文间的知识关联
- **用户友好界面**：提供直观的 Web 界面进行操作

## 技术架构

### 后端技术栈
- **FastAPI**: 高性能 Web 框架
- **GraphRAG**: 微软开源的图检索增强生成框架
- **Deepseek API**: 先进的大语言模型服务
- **Python**: 核心开发语言

### 前端技术栈
- **Vue.js 3**: 响应式前端框架
- **Element Plus**: UI 组件库
- **Axios**: HTTP 客户端

### 支持的文档格式
- PDF 文档
- Word 文档 (.docx)
- 纯文本文件 (.txt)

## 快速开始

### 环境要求
- Python 3.11+
- Conda 环境管理器
- Deepseek API 密钥

### 安装步骤

1. **克隆项目**
```bash
cd /Users/<USER>/Documents/augment-projects/RAG
```

2. **激活 conda 环境**
```bash
conda activate llm_product
```

3. **配置 API 密钥**
编辑 `backend/.env` 文件，设置您的 Deepseek API 密钥：
```env
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here
```

4. **启动后端服务**
```bash
cd backend/app
python main.py
```
服务将在 http://localhost:9000 启动

5. **打开前端界面**
在浏览器中打开：
```
file:///Users/<USER>/Documents/augment-projects/RAG/frontend/index.html
```

## 使用指南

### 1. 上传论文
- 点击"论文上传"标签页
- 拖拽或点击上传您的论文文件
- 系统支持 PDF、DOCX、TXT 格式
- 文件大小限制：50MB

### 2. 生成总结
- 切换到"生成总结"标签页
- 选择要分析的论文
- 选择分析类型：
  - **单篇论文总结**：选择一篇论文
  - **多篇论文综述**：选择多篇论文
- 选择输出语言（中文/英文）
- 可选：添加自定义要求
- 点击"开始生成"

### 3. 查看结果
- 切换到"查看结果"标签页
- 查看生成的总结和综述
- 结果包含详细的分析内容和元数据

## 核心功能

### GraphRAG 知识图谱
系统使用 GraphRAG 技术构建论文间的知识关联：
- 自动提取实体和关系
- 构建知识图谱
- 基于图结构进行检索增强生成

### 智能总结生成
- **单篇论文总结**：
  - 研究背景和问题
  - 主要方法和技术
  - 核心贡献和创新点
  - 实验结果和分析
  - 结论和未来工作

- **多篇论文综述**：
  - 研究领域概述
  - 主要研究方法对比
  - 技术发展趋势
  - 存在问题和挑战
  - 未来研究方向

### 文档处理
- 智能文本提取
- 元数据自动识别
- 多格式支持
- 内容预处理和清洗

## API 文档

### 论文管理 API
- `POST /api/papers/upload` - 上传论文
- `GET /api/papers/` - 获取论文列表
- `GET /api/papers/{id}` - 获取论文详情
- `DELETE /api/papers/{id}` - 删除论文

### 总结生成 API
- `POST /api/summary/generate` - 生成总结
- `GET /api/summary/progress/{task_id}` - 查看生成进度
- `GET /api/summary/` - 获取总结列表
- `GET /api/summary/{id}` - 获取总结详情

## 配置说明

### 环境变量
```env
# 应用配置
DEBUG=true
HOST=0.0.0.0
PORT=9000

# Deepseek API 配置
DEEPSEEK_API_KEY=your_api_key
DEEPSEEK_BASE_URL=https://api.deepseek.com/v1
DEEPSEEK_MODEL=deepseek-chat

# 文件上传配置
MAX_FILE_SIZE=52428800  # 50MB
UPLOAD_DIR=../data/papers

# GraphRAG 配置
GRAPHRAG_WORKING_DIR=../data/graphs
GRAPHRAG_INPUT_DIR=../data/papers
GRAPHRAG_OUTPUT_DIR=../data/summaries
```

## 项目结构

```
RAG/
├── backend/                 # 后端服务
│   ├── app/
│   │   ├── api/            # API 路由
│   │   ├── core/           # 核心配置
│   │   ├── services/       # 业务逻辑
│   │   ├── models/         # 数据模型
│   │   └── main.py         # 应用入口
│   ├── requirements.txt    # Python 依赖
│   └── .env               # 环境配置
├── frontend/               # 前端界面
│   └── index.html         # 主页面
├── data/                   # 数据存储
│   ├── papers/            # 上传的论文
│   ├── graphs/            # 知识图谱
│   └── summaries/         # 生成的总结
└── README.md              # 项目说明
```

## 注意事项

1. **API 密钥**：请确保设置有效的 Deepseek API 密钥
2. **文件格式**：目前支持 PDF、DOCX、TXT 格式
3. **网络连接**：需要稳定的网络连接访问 Deepseek API
4. **处理时间**：复杂论文的分析可能需要几分钟时间
5. **存储空间**：确保有足够的磁盘空间存储上传的文件和生成的图谱

## 故障排除

### 常见问题
1. **服务启动失败**：检查端口是否被占用
2. **API 调用失败**：验证 Deepseek API 密钥是否正确
3. **文件上传失败**：检查文件格式和大小限制
4. **生成失败**：查看后端日志获取详细错误信息

### 日志查看
后端服务的日志会显示在终端中，包含详细的错误信息和调试信息。

## 开发团队

本项目基于现代 AI 技术栈开发，结合了图神经网络、大语言模型和知识图谱技术，为学术研究提供智能化的文献分析工具。
