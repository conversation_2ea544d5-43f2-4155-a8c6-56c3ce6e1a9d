#!/usr/bin/env python3
"""
GraphRAG 论文分析系统测试脚本
"""

import requests
import json
import time
import os

API_BASE = "http://localhost:9000"

def test_health_check():
    """测试健康检查"""
    print("🔍 测试健康检查...")
    try:
        response = requests.get(f"{API_BASE}/health")
        if response.status_code == 200:
            print("✅ 健康检查通过")
            return True
        else:
            print(f"❌ 健康检查失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 健康检查异常: {e}")
        return False

def test_root_endpoint():
    """测试根端点"""
    print("🔍 测试根端点...")
    try:
        response = requests.get(f"{API_BASE}/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 根端点正常: {data['message']}")
            return True
        else:
            print(f"❌ 根端点失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 根端点异常: {e}")
        return False

def test_papers_list():
    """测试论文列表"""
    print("🔍 测试论文列表...")
    try:
        response = requests.get(f"{API_BASE}/api/papers/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 论文列表正常: 共 {data['total']} 篇论文")
            return True
        else:
            print(f"❌ 论文列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 论文列表异常: {e}")
        return False

def test_summaries_list():
    """测试总结列表"""
    print("🔍 测试总结列表...")
    try:
        response = requests.get(f"{API_BASE}/api/summary/")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 总结列表正常: 共 {data['total']} 个总结")
            return True
        else:
            print(f"❌ 总结列表失败: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 总结列表异常: {e}")
        return False

def create_test_file():
    """创建测试文件"""
    test_content = """
# 测试论文

## 摘要
这是一篇测试论文，用于验证 GraphRAG 论文分析系统的功能。

## 引言
随着人工智能技术的快速发展，自然语言处理领域取得了显著进展。

## 方法
本研究采用了深度学习方法，结合了注意力机制和图神经网络。

## 实验
我们在多个数据集上进行了实验，结果表明我们的方法具有良好的性能。

## 结论
本研究提出了一种新的方法，在多个任务上取得了最先进的结果。

## 参考文献
[1] Smith, J. (2023). Deep Learning for NLP. Journal of AI Research.
[2] Zhang, L. (2023). Graph Neural Networks. Conference on Machine Learning.
"""
    
    test_file_path = "test_paper.txt"
    with open(test_file_path, 'w', encoding='utf-8') as f:
        f.write(test_content)
    
    return test_file_path

def test_file_upload():
    """测试文件上传"""
    print("🔍 测试文件上传...")
    
    # 创建测试文件
    test_file_path = create_test_file()
    
    try:
        with open(test_file_path, 'rb') as f:
            files = {'file': ('test_paper.txt', f, 'text/plain')}
            response = requests.post(f"{API_BASE}/api/papers/upload", files=files)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 文件上传成功: {data['title']}")
            return data['id']
        else:
            print(f"❌ 文件上传失败: {response.status_code}")
            if response.text:
                print(f"错误详情: {response.text}")
            return None
    except Exception as e:
        print(f"❌ 文件上传异常: {e}")
        return None
    finally:
        # 清理测试文件
        if os.path.exists(test_file_path):
            os.remove(test_file_path)

def test_summary_generation(paper_id):
    """测试总结生成"""
    print("🔍 测试总结生成...")
    
    if not paper_id:
        print("❌ 没有可用的论文ID")
        return False
    
    try:
        # 生成总结请求
        payload = {
            "paper_ids": [paper_id],
            "summary_type": "single_paper",
            "language": "zh",
            "custom_prompt": "请重点关注研究方法和主要贡献"
        }
        
        response = requests.post(f"{API_BASE}/api/summary/generate", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            task_id = data['task_id']
            print(f"✅ 总结生成任务已启动: {task_id}")
            
            # 轮询进度
            max_attempts = 30  # 最多等待30次，每次2秒
            for attempt in range(max_attempts):
                time.sleep(2)
                progress_response = requests.get(f"{API_BASE}/api/summary/progress/{task_id}")
                
                if progress_response.status_code == 200:
                    progress_data = progress_response.json()
                    print(f"📊 进度: {progress_data['progress']}% - {progress_data['message']}")
                    
                    if progress_data['status'] == 'completed':
                        print("✅ 总结生成完成")
                        return True
                    elif progress_data['status'] == 'error':
                        print(f"❌ 总结生成失败: {progress_data['message']}")
                        return False
                else:
                    print(f"❌ 获取进度失败: {progress_response.status_code}")
                    return False
            
            print("⏰ 总结生成超时")
            return False
        else:
            print(f"❌ 总结生成请求失败: {response.status_code}")
            if response.text:
                print(f"错误详情: {response.text}")
            return False
    except Exception as e:
        print(f"❌ 总结生成异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试 GraphRAG 论文分析系统")
    print("=" * 50)
    
    # 基础功能测试
    tests = [
        test_health_check,
        test_root_endpoint,
        test_papers_list,
        test_summaries_list,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    # 文件上传测试
    print("📁 测试文件上传功能...")
    paper_id = test_file_upload()
    if paper_id:
        passed += 1
        total += 1
        
        # 总结生成测试（需要有效的API密钥）
        print("\n📝 测试总结生成功能...")
        print("⚠️  注意: 此测试需要有效的 Deepseek API 密钥")
        if test_summary_generation(paper_id):
            passed += 1
        total += 1
    else:
        total += 2  # 上传和总结生成都算失败
    
    print("\n" + "=" * 50)
    print(f"🎯 测试完成: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！系统运行正常。")
    else:
        print("⚠️  部分测试失败，请检查系统配置。")
    
    print("\n📋 使用说明:")
    print("1. 确保后端服务在 http://localhost:9000 运行")
    print("2. 在 backend/.env 中配置有效的 DEEPSEEK_API_KEY")
    print("3. 打开前端页面: file:///Users/<USER>/Documents/augment-projects/RAG/frontend/index.html")

if __name__ == "__main__":
    main()
