from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class SummaryType(str, Enum):
    SINGLE_PAPER = "single_paper"
    MULTI_PAPER_REVIEW = "multi_paper_review"

class SummaryStatus(str, Enum):
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    ERROR = "error"

class SummaryRequest(BaseModel):
    paper_ids: List[int] = Field(..., description="List of paper IDs to summarize")
    summary_type: SummaryType = Field(..., description="Type of summary")
    custom_prompt: Optional[str] = Field(default="", description="Custom prompt for generation")
    language: str = Field(default="zh", description="Output language (zh/en)")
    
class SummaryBase(BaseModel):
    title: str = Field(..., description="Summary title")
    content: str = Field(..., description="Generated summary content")
    summary_type: SummaryType
    language: str = "zh"

class SummaryCreate(SummaryBase):
    paper_ids: List[int]
    custom_prompt: Optional[str] = ""
    
class Summary(SummaryBase):
    id: int
    paper_ids: List[int]
    status: SummaryStatus = SummaryStatus.PENDING
    created_time: datetime
    completed_time: Optional[datetime] = None
    custom_prompt: Optional[str] = ""
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        from_attributes = True

class SummaryResponse(BaseModel):
    id: int
    title: str
    content: str
    summary_type: SummaryType
    status: SummaryStatus
    created_time: datetime
    completed_time: Optional[datetime]
    paper_count: int
    language: str

class SummaryListResponse(BaseModel):
    summaries: List[SummaryResponse]
    total: int
    page: int
    size: int

class GenerationProgress(BaseModel):
    task_id: str
    status: SummaryStatus
    progress: int = Field(ge=0, le=100, description="Progress percentage")
    message: str = ""
    estimated_time: Optional[int] = None  # seconds
