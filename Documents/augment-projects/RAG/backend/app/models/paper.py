from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class PaperStatus(str, Enum):
    UPLOADED = "uploaded"
    PROCESSING = "processing"
    PROCESSED = "processed"
    ERROR = "error"

class PaperBase(BaseModel):
    title: str = Field(..., description="Paper title")
    authors: Optional[List[str]] = Field(default=[], description="List of authors")
    abstract: Optional[str] = Field(default="", description="Paper abstract")
    keywords: Optional[List[str]] = Field(default=[], description="Keywords")
    publication_year: Optional[int] = Field(default=None, description="Publication year")
    journal: Optional[str] = Field(default="", description="Journal name")
    doi: Optional[str] = Field(default="", description="DOI")

class PaperCreate(PaperBase):
    filename: str = Field(..., description="Original filename")
    file_path: str = Field(..., description="File storage path")

class PaperUpdate(BaseModel):
    title: Optional[str] = None
    authors: Optional[List[str]] = None
    abstract: Optional[str] = None
    keywords: Optional[List[str]] = None
    publication_year: Optional[int] = None
    journal: Optional[str] = None
    doi: Optional[str] = None
    status: Optional[PaperStatus] = None

class Paper(PaperBase):
    id: int
    filename: str
    file_path: str
    status: PaperStatus = PaperStatus.UPLOADED
    upload_time: datetime
    process_time: Optional[datetime] = None
    file_size: int
    content_hash: str
    metadata: Optional[Dict[str, Any]] = Field(default_factory=dict)
    
    class Config:
        from_attributes = True

class PaperResponse(BaseModel):
    id: int
    title: str
    authors: List[str]
    abstract: str
    filename: str
    status: PaperStatus
    upload_time: datetime
    file_size: int
    
class PaperListResponse(BaseModel):
    papers: List[PaperResponse]
    total: int
    page: int
    size: int
