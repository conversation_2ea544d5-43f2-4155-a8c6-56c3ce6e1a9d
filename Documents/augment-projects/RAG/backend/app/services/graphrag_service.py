import os
import asyncio
import json
from pathlib import Path
from typing import List, Dict, Any, Optional
import pandas as pd
import networkx as nx
from loguru import logger

from app.core.config import settings

class GraphRAGService:
    def __init__(self):
        self.working_dir = Path(settings.GRAPHRAG_WORKING_DIR)
        self.input_dir = Path(settings.GRAPHRAG_INPUT_DIR)
        self.output_dir = Path(settings.GRAPHRAG_OUTPUT_DIR)
        
        # Create directories
        self.working_dir.mkdir(parents=True, exist_ok=True)
        self.input_dir.mkdir(parents=True, exist_ok=True)
        self.output_dir.mkdir(parents=True, exist_ok=True)
    
    async def initialize_graphrag_config(self):
        """Initialize GraphRAG configuration"""
        config = {
            "llm": {
                "api_key": settings.DEEPSEEK_API_KEY,
                "type": "openai_chat",
                "model": settings.DEEPSEEK_MODEL,
                "api_base": settings.DEEPSEEK_BASE_URL,
                "max_tokens": 4000,
                "temperature": 0.0,
                "top_p": 1.0,
                "n": 1,
                "request_timeout": 180.0,
                "api_version": "2023-03-15-preview"
            },
            "parallelization": {
                "stagger": 0.3,
                "num_threads": 50
            },
            "async_mode": "threaded",
            "embeddings": {
                "llm": {
                    "api_key": settings.DEEPSEEK_API_KEY,
                    "type": "openai_embedding",
                    "model": "text-embedding-ada-002",
                    "api_base": settings.DEEPSEEK_BASE_URL
                }
            },
            "chunks": {
                "size": settings.MAX_CHUNK_SIZE,
                "overlap": settings.CHUNK_OVERLAP,
                "group_by_columns": ["id"]
            },
            "input": {
                "type": "file",
                "file_type": "text",
                "base_dir": str(self.input_dir),
                "file_encoding": "utf-8",
                "file_pattern": ".*\\.txt$"
            },
            "cache": {
                "type": "file",
                "base_dir": str(self.working_dir / "cache")
            },
            "storage": {
                "type": "file",
                "base_dir": str(self.output_dir)
            },
            "reporting": {
                "type": "file",
                "base_dir": str(self.working_dir / "reports")
            },
            "entity_extraction": {
                "prompt": "Given a text document that is potentially relevant to this activity and a list of entity types, identify all entities of those types from the text and format your response as a list of json objects with the following format: {\"entity\": <entity_name>, \"type\": <entity_type>, \"description\": <entity_description>}",
                "entity_types": ["organization", "person", "geo", "event"],
                "max_gleanings": 1
            },
            "summarize_descriptions": {
                "prompt": "You are a helpful assistant responsible for generating a comprehensive summary of the data provided below.\nGiven one or more entities, and a list of descriptions, all related to the same entity or group of entities.\nPlease concatenate all of these into a single, comprehensive description.\n\n#######\n-Data-\nEntities: {entity_name}\nDescription List: {description_list}\n#######\nOutput:",
                "max_length": 500
            },
            "claim_extraction": {
                "prompt": "Given a text document that is potentially relevant to this activity, an entity specification, and a claim description, extract all entities that match the entity specification and all claims that match the claim description from the text.\n\nEntity Specification: {entity_spec}\nClaim Description: {claim_description}\nText: {input_text}\n\nOutput:",
                "description": "Any claims or facts that could be relevant to information discovery.",
                "max_gleanings": 1
            },
            "community_detection": {
                "max_cluster_size": 10
            },
            "cluster_graph": {
                "max_cluster_size": 10
            },
            "embed_graph": {
                "enabled": false
            },
            "umap": {
                "enabled": false
            },
            "snapshots": {
                "graphml": false,
                "raw_entities": false,
                "top_level_nodes": false
            },
            "local_search": {
                "text_unit_prop": 0.5,
                "community_prop": 0.1,
                "conversation_history_max_turns": 5,
                "top_k_mapped_entities": 10,
                "top_k_relationships": 10,
                "max_tokens": 12000
            },
            "global_search": {
                "max_tokens": 12000,
                "data_max_tokens": 12000,
                "map_max_tokens": 1000,
                "reduce_max_tokens": 2000,
                "concurrency": 32
            }
        }
        
        config_path = self.working_dir / "settings.yaml"
        with open(config_path, 'w', encoding='utf-8') as f:
            import yaml
            yaml.dump(config, f, default_flow_style=False, allow_unicode=True)
        
        return config_path
    
    async def prepare_input_data(self, papers_data: List[Dict[str, Any]]) -> List[str]:
        """Prepare input data for GraphRAG processing"""
        input_files = []
        
        for i, paper in enumerate(papers_data):
            # Create input file for each paper
            filename = f"paper_{i+1}_{paper.get('id', 'unknown')}.txt"
            file_path = self.input_dir / filename
            
            # Prepare content
            content = f"Title: {paper.get('title', 'Unknown')}\n\n"
            if paper.get('authors'):
                content += f"Authors: <AUTHORS>
            if paper.get('abstract'):
                content += f"Abstract: {paper['abstract']}\n\n"
            content += f"Content:\n{paper.get('text', '')}"
            
            # Write to file
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            input_files.append(str(file_path))
        
        return input_files
    
    async def run_graphrag_indexing(self) -> bool:
        """Run GraphRAG indexing process"""
        try:
            # Initialize config
            config_path = await self.initialize_graphrag_config()
            
            # Run GraphRAG indexing
            cmd = f"python -m graphrag.index --root {self.working_dir}"
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.working_dir
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                logger.info("GraphRAG indexing completed successfully")
                return True
            else:
                logger.error(f"GraphRAG indexing failed: {stderr.decode()}")
                return False
                
        except Exception as e:
            logger.error(f"Error running GraphRAG indexing: {str(e)}")
            return False
    
    async def query_graphrag(
        self,
        query: str,
        search_type: str = "local"  # "local" or "global"
    ) -> str:
        """Query GraphRAG for information"""
        try:
            cmd = f"python -m graphrag.query --root {self.working_dir} --method {search_type} \"{query}\""
            process = await asyncio.create_subprocess_shell(
                cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE,
                cwd=self.working_dir
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                return stdout.decode().strip()
            else:
                logger.error(f"GraphRAG query failed: {stderr.decode()}")
                return ""
                
        except Exception as e:
            logger.error(f"Error querying GraphRAG: {str(e)}")
            return ""
    
    async def get_knowledge_graph_summary(self) -> Dict[str, Any]:
        """Get summary of the knowledge graph"""
        try:
            # Read entities and relationships
            entities_file = self.output_dir / "create_final_entities.parquet"
            relationships_file = self.output_dir / "create_final_relationships.parquet"
            
            summary = {
                "entities_count": 0,
                "relationships_count": 0,
                "communities_count": 0,
                "top_entities": [],
                "entity_types": {}
            }
            
            if entities_file.exists():
                entities_df = pd.read_parquet(entities_file)
                summary["entities_count"] = len(entities_df)
                
                # Get top entities by degree
                if not entities_df.empty:
                    summary["top_entities"] = entities_df.head(10)["title"].tolist()
                    
                    # Count entity types
                    if "type" in entities_df.columns:
                        summary["entity_types"] = entities_df["type"].value_counts().to_dict()
            
            if relationships_file.exists():
                relationships_df = pd.read_parquet(relationships_file)
                summary["relationships_count"] = len(relationships_df)
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting knowledge graph summary: {str(e)}")
            return {}
    
    async def extract_relevant_context(
        self,
        papers_data: List[Dict[str, Any]],
        query: str
    ) -> str:
        """Extract relevant context for paper analysis"""
        try:
            # Prepare input data
            await self.prepare_input_data(papers_data)
            
            # Run indexing
            indexing_success = await self.run_graphrag_indexing()
            if not indexing_success:
                return ""
            
            # Query for relevant information
            context = await self.query_graphrag(query, "global")
            return context
            
        except Exception as e:
            logger.error(f"Error extracting relevant context: {str(e)}")
            return ""
