import os
import hashlib
import aiofiles
from pathlib import Path
from typing import List, Optional, Dict, Any
import PyPDF2
import pdfplumber
from docx import Document
import re

from core.config import settings
from models.paper import Paper, PaperCreate, PaperStatus

class PaperService:
    def __init__(self):
        self.upload_dir = Path(settings.UPLOAD_DIR)
        self.upload_dir.mkdir(parents=True, exist_ok=True)
    
    async def save_uploaded_file(self, file_content: bytes, filename: str) -> str:
        """Save uploaded file and return file path"""
        # Generate unique filename
        file_hash = hashlib.md5(file_content).hexdigest()
        file_ext = Path(filename).suffix
        unique_filename = f"{file_hash}{file_ext}"
        file_path = self.upload_dir / unique_filename
        
        # Save file
        async with aiofiles.open(file_path, 'wb') as f:
            await f.write(file_content)
        
        return str(file_path)
    
    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF file"""
        text = ""
        try:
            # Try with pdfplumber first (better for complex layouts)
            with pdfplumber.open(file_path) as pdf:
                for page in pdf.pages:
                    page_text = page.extract_text()
                    if page_text:
                        text += page_text + "\n"
        except Exception:
            # Fallback to PyPDF2
            try:
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    for page in pdf_reader.pages:
                        text += page.extract_text() + "\n"
            except Exception as e:
                raise Exception(f"Failed to extract text from PDF: {str(e)}")
        
        return text.strip()
    
    def extract_text_from_docx(self, file_path: str) -> str:
        """Extract text from DOCX file"""
        try:
            doc = Document(file_path)
            text = ""
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            return text.strip()
        except Exception as e:
            raise Exception(f"Failed to extract text from DOCX: {str(e)}")
    
    def extract_text_from_txt(self, file_path: str) -> str:
        """Extract text from TXT file"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except UnicodeDecodeError:
            # Try with different encoding
            with open(file_path, 'r', encoding='gbk') as file:
                return file.read().strip()
        except Exception as e:
            raise Exception(f"Failed to extract text from TXT: {str(e)}")
    
    def extract_text(self, file_path: str) -> str:
        """Extract text from file based on extension"""
        file_ext = Path(file_path).suffix.lower()
        
        if file_ext == '.pdf':
            return self.extract_text_from_pdf(file_path)
        elif file_ext == '.docx':
            return self.extract_text_from_docx(file_path)
        elif file_ext == '.txt':
            return self.extract_text_from_txt(file_path)
        else:
            raise Exception(f"Unsupported file format: {file_ext}")
    
    def extract_metadata(self, text: str, filename: str) -> Dict[str, Any]:
        """Extract metadata from paper text"""
        metadata = {
            "title": "",
            "authors": [],
            "abstract": "",
            "keywords": [],
            "publication_year": None,
            "journal": "",
            "doi": ""
        }
        
        # Simple heuristic extraction (can be improved with NLP)
        lines = text.split('\n')
        
        # Try to find title (usually first non-empty line)
        for line in lines:
            line = line.strip()
            if line and len(line) > 10:
                metadata["title"] = line
                break
        
        # If no title found, use filename
        if not metadata["title"]:
            metadata["title"] = Path(filename).stem
        
        # Try to find abstract
        abstract_pattern = r'(?i)abstract[:\s]*(.*?)(?=\n\s*\n|\n\s*keywords|\n\s*introduction|$)'
        abstract_match = re.search(abstract_pattern, text, re.DOTALL)
        if abstract_match:
            metadata["abstract"] = abstract_match.group(1).strip()
        
        # Try to find DOI
        doi_pattern = r'(?i)doi[:\s]*([0-9]+\.[0-9]+/[^\s]+)'
        doi_match = re.search(doi_pattern, text)
        if doi_match:
            metadata["doi"] = doi_match.group(1)
        
        # Try to find year
        year_pattern = r'\b(19|20)\d{2}\b'
        year_matches = re.findall(year_pattern, text)
        if year_matches:
            metadata["publication_year"] = int(year_matches[0] + year_matches[0][2:])
        
        return metadata
    
    async def process_paper(self, file_content: bytes, filename: str) -> Dict[str, Any]:
        """Process uploaded paper file"""
        # Save file
        file_path = await self.save_uploaded_file(file_content, filename)
        
        # Extract text
        text = self.extract_text(file_path)
        
        # Extract metadata
        metadata = self.extract_metadata(text, filename)
        
        # Calculate file hash
        content_hash = hashlib.md5(file_content).hexdigest()
        
        return {
            "filename": filename,
            "file_path": file_path,
            "file_size": len(file_content),
            "content_hash": content_hash,
            "text": text,
            "metadata": metadata
        }
