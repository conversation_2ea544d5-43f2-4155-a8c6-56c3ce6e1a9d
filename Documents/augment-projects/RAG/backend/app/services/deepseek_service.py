import httpx
import async<PERSON>
from typing import List, Dict, Any, Optional
import json
from loguru import logger

from core.config import settings

class DeepseekService:
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.base_url = settings.DEEPSEEK_BASE_URL
        self.model = settings.DEEPSEEK_MODEL
        self.client = httpx.AsyncClient(timeout=60.0)
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()
    
    async def generate_completion(
        self,
        messages: List[Dict[str, str]],
        max_tokens: int = 2000,
        temperature: float = 0.7,
        stream: bool = False
    ) -> str:
        """Generate completion using Deepseek API"""
        if not self.api_key:
            raise ValueError("Deepseek API key not configured")
        
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        payload = {
            "model": self.model,
            "messages": messages,
            "max_tokens": max_tokens,
            "temperature": temperature,
            "stream": stream
        }
        
        try:
            response = await self.client.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload
            )
            response.raise_for_status()
            
            result = response.json()
            return result["choices"][0]["message"]["content"]
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Deepseek API error: {e.response.status_code} - {e.response.text}")
            raise Exception(f"Deepseek API error: {e.response.status_code}")
        except Exception as e:
            logger.error(f"Error calling Deepseek API: {str(e)}")
            raise Exception(f"Error calling Deepseek API: {str(e)}")
    
    async def summarize_single_paper(
        self,
        paper_text: str,
        paper_title: str = "",
        language: str = "zh",
        custom_prompt: str = ""
    ) -> str:
        """Generate summary for a single paper"""
        
        # Prepare system prompt
        if language == "zh":
            system_prompt = """你是一个专业的学术论文分析专家。请对给定的论文进行全面的总结分析，包括：
1. 研究背景和问题
2. 主要方法和技术
3. 核心贡献和创新点
4. 实验结果和分析
5. 结论和未来工作

请用中文回答，保持学术性和准确性。"""
        else:
            system_prompt = """You are a professional academic paper analysis expert. Please provide a comprehensive summary of the given paper, including:
1. Research background and problem
2. Main methods and techniques
3. Core contributions and innovations
4. Experimental results and analysis
5. Conclusions and future work

Please maintain academic rigor and accuracy."""
        
        # Prepare user prompt
        user_prompt = f"""论文标题: {paper_title}\n\n论文内容:\n{paper_text[:8000]}"""  # Limit text length
        
        if custom_prompt:
            user_prompt += f"\n\n特殊要求: {custom_prompt}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        return await self.generate_completion(messages, max_tokens=3000)
    
    async def generate_literature_review(
        self,
        papers_data: List[Dict[str, Any]],
        language: str = "zh",
        custom_prompt: str = ""
    ) -> str:
        """Generate literature review for multiple papers"""
        
        # Prepare system prompt
        if language == "zh":
            system_prompt = """你是一个专业的学术综述写作专家。请基于给定的多篇论文，撰写一份高质量的文献综述，包括：
1. 研究领域概述
2. 主要研究方法和技术发展
3. 各论文的核心贡献对比
4. 研究趋势和发展方向
5. 存在的问题和挑战
6. 未来研究展望

请保持学术性、客观性和逻辑性。"""
        else:
            system_prompt = """You are a professional literature review expert. Please write a high-quality literature review based on the given papers, including:
1. Research field overview
2. Main research methods and technical developments
3. Comparison of core contributions from each paper
4. Research trends and directions
5. Existing problems and challenges
6. Future research prospects

Please maintain academic rigor, objectivity, and logical structure."""
        
        # Prepare papers summary
        papers_summary = ""
        for i, paper in enumerate(papers_data, 1):
            papers_summary += f"\n论文{i}: {paper.get('title', 'Unknown')}\n"
            papers_summary += f"摘要: {paper.get('abstract', 'No abstract')}\n"
            papers_summary += f"主要内容: {paper.get('text', '')[:2000]}...\n"
            papers_summary += "-" * 50 + "\n"
        
        user_prompt = f"""请基于以下{len(papers_data)}篇论文撰写文献综述:\n{papers_summary}"""
        
        if custom_prompt:
            user_prompt += f"\n\n特殊要求: {custom_prompt}"
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        return await self.generate_completion(messages, max_tokens=4000)
    
    async def extract_key_information(self, text: str) -> Dict[str, Any]:
        """Extract key information from paper text"""
        system_prompt = """请从给定的论文文本中提取关键信息，以JSON格式返回：
{
    "title": "论文标题",
    "authors": ["作者1", "作者2"],
    "keywords": ["关键词1", "关键词2"],
    "main_contributions": ["贡献1", "贡献2"],
    "methodology": "主要方法",
    "results": "主要结果"
}"""
        
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": text[:4000]}
        ]
        
        try:
            response = await self.generate_completion(messages, max_tokens=1000)
            return json.loads(response)
        except:
            return {}
