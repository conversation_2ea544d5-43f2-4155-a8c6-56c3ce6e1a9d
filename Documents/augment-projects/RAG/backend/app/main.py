from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import uvicorn
import os
from pathlib import Path

from app.api import papers, summary
from app.core.config import settings

# Create FastAPI app
app = FastAPI(
    title="GraphRAG Paper Analysis System",
    description="A system for paper summarization and review generation using GraphRAG and Deepseek",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.ALLOWED_HOSTS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create data directories
data_dir = Path("../data")
data_dir.mkdir(exist_ok=True)
(data_dir / "papers").mkdir(exist_ok=True)
(data_dir / "graphs").mkdir(exist_ok=True)
(data_dir / "summaries").mkdir(exist_ok=True)

# Mount static files
app.mount("/static", StaticFiles(directory="../data"), name="static")

# Include routers
app.include_router(papers.router, prefix="/api/papers", tags=["papers"])
app.include_router(summary.router, prefix="/api/summary", tags=["summary"])

@app.get("/")
async def root():
    return {
        "message": "GraphRAG Paper Analysis System",
        "version": "1.0.0",
        "docs": "/docs"
    }

@app.get("/health")
async def health_check():
    return {"status": "healthy"}

if __name__ == "__main__":
    uvicorn.run(
        "main:app",
        host=settings.HOST,
        port=settings.PORT,
        reload=settings.DEBUG,
        log_level="info"
    )
