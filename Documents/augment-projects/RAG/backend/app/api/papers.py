from fastapi import APIRouter, UploadFile, File, HTTPException, Depends, Query
from fastapi.responses import <PERSON><PERSON><PERSON><PERSON>ponse
from typing import List, Optional
import os
from pathlib import Path

from app.models.paper import Paper, PaperCreate, PaperResponse, PaperListResponse, PaperStatus
from app.services.paper_service import PaperService
from app.core.config import settings

router = APIRouter()

# In-memory storage for demo (replace with database in production)
papers_db: List[Paper] = []
paper_id_counter = 1

def get_paper_service():
    return PaperService()

@router.post("/upload", response_model=PaperResponse)
async def upload_paper(
    file: UploadFile = File(...),
    paper_service: PaperService = Depends(get_paper_service)
):
    """Upload a paper file"""
    global paper_id_counter
    
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="No file provided")
    
    file_ext = Path(file.filename).suffix.lower()
    if file_ext not in settings.ALLOWED_EXTENSIONS:
        raise HTTPException(
            status_code=400, 
            detail=f"File type {file_ext} not supported. Allowed types: {settings.ALLOWED_EXTENSIONS}"
        )
    
    # Check file size
    file_content = await file.read()
    if len(file_content) > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=400,
            detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE / 1024 / 1024}MB"
        )
    
    try:
        # Process paper
        result = await paper_service.process_paper(file_content, file.filename)
        
        # Create paper record
        paper_data = PaperCreate(
            filename=result["filename"],
            file_path=result["file_path"],
            title=result["metadata"].get("title", file.filename),
            authors=result["metadata"].get("authors", []),
            abstract=result["metadata"].get("abstract", ""),
            keywords=result["metadata"].get("keywords", []),
            publication_year=result["metadata"].get("publication_year"),
            journal=result["metadata"].get("journal", ""),
            doi=result["metadata"].get("doi", "")
        )
        
        # Create paper object
        paper = Paper(
            id=paper_id_counter,
            **paper_data.dict(),
            status=PaperStatus.PROCESSED,
            upload_time=datetime.now(),
            file_size=result["file_size"],
            content_hash=result["content_hash"],
            metadata=result["metadata"]
        )
        
        papers_db.append(paper)
        paper_id_counter += 1
        
        return PaperResponse(
            id=paper.id,
            title=paper.title,
            authors=paper.authors,
            abstract=paper.abstract,
            filename=paper.filename,
            status=paper.status,
            upload_time=paper.upload_time,
            file_size=paper.file_size
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")

@router.get("/", response_model=PaperListResponse)
async def list_papers(
    page: int = Query(1, ge=1),
    size: int = Query(10, ge=1, le=100)
):
    """List all papers with pagination"""
    start = (page - 1) * size
    end = start + size
    
    papers_page = papers_db[start:end]
    
    paper_responses = [
        PaperResponse(
            id=paper.id,
            title=paper.title,
            authors=paper.authors,
            abstract=paper.abstract,
            filename=paper.filename,
            status=paper.status,
            upload_time=paper.upload_time,
            file_size=paper.file_size
        )
        for paper in papers_page
    ]
    
    return PaperListResponse(
        papers=paper_responses,
        total=len(papers_db),
        page=page,
        size=size
    )

@router.get("/{paper_id}", response_model=PaperResponse)
async def get_paper(paper_id: int):
    """Get paper by ID"""
    paper = next((p for p in papers_db if p.id == paper_id), None)
    if not paper:
        raise HTTPException(status_code=404, detail="Paper not found")
    
    return PaperResponse(
        id=paper.id,
        title=paper.title,
        authors=paper.authors,
        abstract=paper.abstract,
        filename=paper.filename,
        status=paper.status,
        upload_time=paper.upload_time,
        file_size=paper.file_size
    )

@router.delete("/{paper_id}")
async def delete_paper(paper_id: int):
    """Delete paper by ID"""
    global papers_db
    
    paper = next((p for p in papers_db if p.id == paper_id), None)
    if not paper:
        raise HTTPException(status_code=404, detail="Paper not found")
    
    # Remove file
    try:
        if os.path.exists(paper.file_path):
            os.remove(paper.file_path)
    except Exception as e:
        print(f"Warning: Could not delete file {paper.file_path}: {e}")
    
    # Remove from database
    papers_db = [p for p in papers_db if p.id != paper_id]
    
    return {"message": "Paper deleted successfully"}

@router.get("/{paper_id}/content")
async def get_paper_content(paper_id: int):
    """Get paper content"""
    paper = next((p for p in papers_db if p.id == paper_id), None)
    if not paper:
        raise HTTPException(status_code=404, detail="Paper not found")
    
    try:
        paper_service = PaperService()
        text = paper_service.extract_text(paper.file_path)
        return {"content": text}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error reading paper content: {str(e)}")

# Import datetime at the top
from datetime import datetime
