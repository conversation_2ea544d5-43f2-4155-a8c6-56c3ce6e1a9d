from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from typing import List, Dict, Any
import asyncio
import uuid
from datetime import datetime

from app.models.summary import (
    SummaryRequest, Summary, SummaryResponse, SummaryListResponse,
    SummaryType, SummaryStatus, GenerationProgress
)
from app.services.deepseek_service import DeepseekService
from app.services.graphrag_service import GraphRAGService
from app.services.paper_service import PaperService
from app.api.papers import papers_db

router = APIRouter()

# In-memory storage for demo
summaries_db: List[Summary] = []
summary_id_counter = 1
generation_tasks: Dict[str, GenerationProgress] = {}

def get_deepseek_service():
    return DeepseekService()

def get_graphrag_service():
    return GraphRAGService()

def get_paper_service():
    return PaperService()

async def generate_summary_task(
    task_id: str,
    request: SummaryRequest,
    deepseek_service: DeepseekService,
    graphrag_service: GraphRAGService,
    paper_service: PaperService
):
    """Background task for generating summary"""
    global summary_id_counter
    
    try:
        # Update progress
        generation_tasks[task_id].status = SummaryStatus.PROCESSING
        generation_tasks[task_id].progress = 10
        generation_tasks[task_id].message = "正在获取论文数据..."
        
        # Get papers data
        papers_data = []
        for paper_id in request.paper_ids:
            paper = next((p for p in papers_db if p.id == paper_id), None)
            if paper:
                # Extract text content
                text = paper_service.extract_text(paper.file_path)
                papers_data.append({
                    "id": paper.id,
                    "title": paper.title,
                    "authors": paper.authors,
                    "abstract": paper.abstract,
                    "text": text
                })
        
        if not papers_data:
            raise Exception("No valid papers found")
        
        generation_tasks[task_id].progress = 30
        generation_tasks[task_id].message = "正在构建知识图谱..."
        
        # Generate summary based on type
        if request.summary_type == SummaryType.SINGLE_PAPER:
            if len(papers_data) != 1:
                raise Exception("Single paper summary requires exactly one paper")
            
            generation_tasks[task_id].progress = 50
            generation_tasks[task_id].message = "正在生成论文总结..."
            
            paper = papers_data[0]
            async with deepseek_service:
                content = await deepseek_service.summarize_single_paper(
                    paper["text"],
                    paper["title"],
                    request.language,
                    request.custom_prompt
                )
            
            title = f"{paper['title']} - 论文总结"
            
        else:  # MULTI_PAPER_REVIEW
            generation_tasks[task_id].progress = 40
            generation_tasks[task_id].message = "正在分析多篇论文关系..."
            
            # Use GraphRAG for enhanced context
            context = await graphrag_service.extract_relevant_context(
                papers_data,
                "研究方法、主要贡献、技术创新"
            )
            
            generation_tasks[task_id].progress = 70
            generation_tasks[task_id].message = "正在生成文献综述..."
            
            # Add context to papers data if available
            if context:
                for paper in papers_data:
                    paper["context"] = context
            
            async with deepseek_service:
                content = await deepseek_service.generate_literature_review(
                    papers_data,
                    request.language,
                    request.custom_prompt
                )
            
            title = f"文献综述 - {len(papers_data)}篇论文"
        
        generation_tasks[task_id].progress = 90
        generation_tasks[task_id].message = "正在保存结果..."
        
        # Create summary record
        summary = Summary(
            id=summary_id_counter,
            title=title,
            content=content,
            summary_type=request.summary_type,
            language=request.language,
            paper_ids=request.paper_ids,
            status=SummaryStatus.COMPLETED,
            created_time=datetime.now(),
            completed_time=datetime.now(),
            custom_prompt=request.custom_prompt
        )
        
        summaries_db.append(summary)
        summary_id_counter += 1
        
        # Update progress
        generation_tasks[task_id].status = SummaryStatus.COMPLETED
        generation_tasks[task_id].progress = 100
        generation_tasks[task_id].message = "生成完成"
        
    except Exception as e:
        generation_tasks[task_id].status = SummaryStatus.ERROR
        generation_tasks[task_id].message = f"生成失败: {str(e)}"

@router.post("/generate")
async def generate_summary(
    request: SummaryRequest,
    background_tasks: BackgroundTasks,
    deepseek_service: DeepseekService = Depends(get_deepseek_service),
    graphrag_service: GraphRAGService = Depends(get_graphrag_service),
    paper_service: PaperService = Depends(get_paper_service)
):
    """Generate summary or literature review"""
    
    # Validate papers exist
    for paper_id in request.paper_ids:
        paper = next((p for p in papers_db if p.id == paper_id), None)
        if not paper:
            raise HTTPException(status_code=404, detail=f"Paper {paper_id} not found")
    
    # Validate request
    if request.summary_type == SummaryType.SINGLE_PAPER and len(request.paper_ids) != 1:
        raise HTTPException(
            status_code=400,
            detail="Single paper summary requires exactly one paper"
        )
    
    if request.summary_type == SummaryType.MULTI_PAPER_REVIEW and len(request.paper_ids) < 2:
        raise HTTPException(
            status_code=400,
            detail="Literature review requires at least two papers"
        )
    
    # Create task
    task_id = str(uuid.uuid4())
    generation_tasks[task_id] = GenerationProgress(
        task_id=task_id,
        status=SummaryStatus.PENDING,
        progress=0,
        message="任务已创建，等待处理..."
    )
    
    # Start background task
    background_tasks.add_task(
        generate_summary_task,
        task_id,
        request,
        deepseek_service,
        graphrag_service,
        paper_service
    )
    
    return {"task_id": task_id, "message": "Summary generation started"}

@router.get("/progress/{task_id}", response_model=GenerationProgress)
async def get_generation_progress(task_id: str):
    """Get generation progress"""
    if task_id not in generation_tasks:
        raise HTTPException(status_code=404, detail="Task not found")
    
    return generation_tasks[task_id]

@router.get("/", response_model=SummaryListResponse)
async def list_summaries(page: int = 1, size: int = 10):
    """List all summaries with pagination"""
    start = (page - 1) * size
    end = start + size
    
    summaries_page = summaries_db[start:end]
    
    summary_responses = [
        SummaryResponse(
            id=summary.id,
            title=summary.title,
            content=summary.content[:500] + "..." if len(summary.content) > 500 else summary.content,
            summary_type=summary.summary_type,
            status=summary.status,
            created_time=summary.created_time,
            completed_time=summary.completed_time,
            paper_count=len(summary.paper_ids),
            language=summary.language
        )
        for summary in summaries_page
    ]
    
    return SummaryListResponse(
        summaries=summary_responses,
        total=len(summaries_db),
        page=page,
        size=size
    )

@router.get("/{summary_id}", response_model=SummaryResponse)
async def get_summary(summary_id: int):
    """Get summary by ID"""
    summary = next((s for s in summaries_db if s.id == summary_id), None)
    if not summary:
        raise HTTPException(status_code=404, detail="Summary not found")
    
    return SummaryResponse(
        id=summary.id,
        title=summary.title,
        content=summary.content,
        summary_type=summary.summary_type,
        status=summary.status,
        created_time=summary.created_time,
        completed_time=summary.completed_time,
        paper_count=len(summary.paper_ids),
        language=summary.language
    )

@router.delete("/{summary_id}")
async def delete_summary(summary_id: int):
    """Delete summary by ID"""
    global summaries_db
    
    summary = next((s for s in summaries_db if s.id == summary_id), None)
    if not summary:
        raise HTTPException(status_code=404, detail="Summary not found")
    
    summaries_db = [s for s in summaries_db if s.id != summary_id]
    
    return {"message": "Summary deleted successfully"}
